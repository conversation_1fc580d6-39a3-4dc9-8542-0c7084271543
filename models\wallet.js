'use strict';
module.exports = (sequelize, DataTypes) => {
  const Wallet = sequelize.define('Wallet', {
    wallet_id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4
    },
    user_id: {
      type: DataTypes.UUID,
      allowNull: false,
      unique: true,
      references: { model: 'user', key: 'user_id' }
    },
    balance: DataTypes.DECIMAL(15, 2),
    bonus_balance: DataTypes.DECIMAL(15, 2),
    total_deposited: DataTypes.DECIMAL(15, 2),
    total_withdrawn: DataTypes.DECIMAL(15, 2),
    total_invested: DataTypes.DECIMAL(15, 2),
    is_active: DataTypes.BOOLEAN
  }, {
    modelName: 'Wallet',
    tableName: 'wallet'
  });

  Wallet.associate = (models) => {
    Wallet.belongsTo(models.User, { foreignKey: 'user_id' });
  };

  return Wallet;
};