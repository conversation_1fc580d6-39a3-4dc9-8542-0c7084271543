'use strict';
const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Investment = sequelize.define('Investment', {
    investment_id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
      allowNull: false
    },
    user_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: { 
        model: 'user', 
        key: 'user_id' 
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    },
    plan_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: { 
        model: 'plan', 
        key: 'plan_id' 
      },
      onDelete: 'RESTRICT',
      onUpdate: 'CASCADE'
    },
    amount: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      validate: {
        min: 0.01
      }
    },
    start_date: {
      type: DataTypes.DATE,
      allowNull: false
    },
    end_date: {
      type: DataTypes.DATE,
      allowNull: false
    },
    status: {
      type: DataTypes.ENUM('active', 'completed', 'cancelled'),
      allowNull: false,
      defaultValue: 'active'
    },
    payment_method: {
      type: DataTypes.ENUM('wallet', 'external'),
      allowNull: false,
      defaultValue: 'wallet'
    },
    transaction_id: {
      type: DataTypes.STRING(100),
      allowNull: true
    },
    is_first_investment: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Track if this is users first investment for referral bonus'
    },
    expected_return: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
      comment: 'Calculated expected return amount'
    },
    actual_return: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
      comment: 'Actual return amount when completed'
    }
  }, {
    tableName: 'investment',
    timestamps: true,
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['plan_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['end_date']
      },
      {
        fields: ['is_first_investment']
      }
    ]
  });

  Investment.associate = (models) => {
    Investment.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });
    Investment.belongsTo(models.Plan, {
      foreignKey: 'plan_id',
      as: 'plan'
    });
    Investment.hasMany(models.Bonus, {
      foreignKey: 'source_investment_id',
      as: 'bonuses'
    });
  };

  // Helper method to calculate expected return
  Investment.prototype.calculateExpectedReturn = function(plan) {
    const principal = parseFloat(this.amount);
    const returnPercentage = parseFloat(plan.return_percentage);
    const extraReturnPercentage = parseFloat(plan.extra_return_percentage || 0);
    
    const totalReturnPercentage = returnPercentage + extraReturnPercentage;
    const expectedReturn = principal + (principal * totalReturnPercentage / 100);
    
    return expectedReturn;
  };

  return Investment;
};
