const { User, Referral } = require('../models');
const BaseController = require('../helpers/BaseController');
const { Op } = require('sequelize');

exports.getProfile = async (req, res) => {
  try {
    const userId = req.user.id;

    const user = await User.findByPk(userId, {
      attributes: ['user_id', 'user_name', 'name', 'email', 'mobile', 'referral_code', 'createdAt']
    });

    if (!user) {
      return BaseController.sendError(res, 'User not found');
    }

    return BaseController.sendResponse(res, user, 'User profile fetched successfully');
  } catch (err) {
    console.error('Get profile error:', err);
    return BaseController.sendError(res, 'Server error', [{ error: err.message }]);
  }
};

exports.getReferralCount = async (req, res) => {
  try {
    const userId = req.user.id;

    // Check if user exists
    const user = await User.findByPk(userId, {
      attributes: ['user_id', 'user_name']
    });

    if (!user) {
      return BaseController.sendError(res, 'User not found');
    }

    // Get level 1 count
    const level1Count = await Referral.count({
      where: {
        referrer_id: userId,
        level: 1,
        status: 'active'
      }
    });

    // Get level 2 & 3 combined count
    const level2And3Count = await Referral.count({
      where: {
        referrer_id: userId,
        level: {
          [Op.in]: [2, 3]
        },
        status: 'active'
      }
    });

    const referralData = {
      level_1: level1Count,
      level_2_and_3: level2And3Count,
      total: level1Count + level2And3Count
    };

    return BaseController.sendResponse(res, referralData, 'Referral count fetched successfully');
  } catch (err) {
    console.error('Get referral count error:', err);
    return BaseController.sendError(res, 'Server error', [{ error: err.message }]);
  }
};
