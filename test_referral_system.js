const { User, Wallet, Plan, Investment, Bonus, Transaction } = require('./models');
const { Op } = require('sequelize');

async function testReferralSystem() {
    console.log('🧪 Testing Referral System Changes...\n');

    try {
        // Clean up test data first
        await cleanupTestData();

        // Test 1: Create test users
        console.log('1️⃣ Creating test users...');
        const referrer = await User.create({
            name: 'Test Referrer',
            user_name: 'test_referrer',
            email: '<EMAIL>',
            mobile: '1234567890',
            password: 'password123',
            referral_code: ''
        });

        const referrerWallet = await Wallet.create({
            user_id: referrer.user_id,
            balance: 1000.00,
            bonus_balance: 0.00,
            total_deposited: 1000.00,
            total_withdrawn: 0.00,
            total_invested: 0.00,
            is_active: true
        });

        const newUser = await User.create({
            name: 'Test New User',
            user_name: 'test_newuser',
            email: '<EMAIL>',
            mobile: '0987654321',
            password: 'password123',
            referral_code: '',
            referred_by: referrer.user_id
        });

        const newUserWallet = await Wallet.create({
            user_id: newUser.user_id,
            balance: 500.00,
            bonus_balance: 0.00,
            total_deposited: 500.00,
            total_withdrawn: 0.00,
            total_invested: 0.00,
            is_active: true
        });

        console.log('✅ Test users created successfully');

        // Test 2: Create test plan
        console.log('\n2️⃣ Creating test plan...');
        const testPlan = await Plan.create({
            name: 'Test Plan',
            description: 'Test plan for referral system testing',
            amount: 100.00,
            duration: 30,
            return_percentage: 10.00,
            extra_return_percentage: 0.00,
            investment_amount: 100.00,
            is_active: true
        });

        console.log('✅ Test plan created successfully');

        // Test 3: Simulate first investment by new user
        console.log('\n3️⃣ Simulating first investment by new user...');
        
        const transaction = await Investment.sequelize.transaction();
        
        try {
            // Create investment
            const investment = await Investment.create({
                user_id: newUser.user_id,
                plan_id: testPlan.plan_id,
                amount: 100.00,
                start_date: new Date(),
                end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
                status: 'active',
                payment_method: 'wallet',
                is_first_investment: true,
                expected_return: 110.00
            }, { transaction });

            // Update new user wallet (deduct investment amount)
            await newUserWallet.update({
                balance: parseFloat(newUserWallet.balance) - 100.00,
                total_invested: parseFloat(newUserWallet.total_invested || 0) + 100.00
            }, { transaction });

            // Create investment transaction record
            await Transaction.create({
                user_id: newUser.user_id,
                transaction_type: 'investment',
                amount: 100.00,
                balance_type: 'main_balance',
                transaction_status: 'completed',
                description: `Investment in ${testPlan.name} plan`,
                reference_id: investment.investment_id,
                reference_type: 'investment',
                payment_method: 'wallet',
                processed_at: new Date()
            }, { transaction });

            // Handle referral bonus - ONLY referrer gets 10 points
            const bonusAmount = 10; // Fixed 10 points

            // Create bonus record for referrer
            await Bonus.create({
                user_id: referrer.user_id,
                bonus_type: 'referral_bonus_referrer',
                amount: bonusAmount,
                source_investment_id: investment.investment_id,
                source_user_id: newUser.user_id,
                description: `Referral bonus for referring ${newUser.user_name} who made first investment`,
                status: 'completed',
                processed_at: new Date()
            }, { transaction });

            // Update referrer wallet
            await referrerWallet.update({
                bonus_balance: parseFloat(referrerWallet.bonus_balance) + bonusAmount
            }, { transaction });

            // Create transaction record for referrer bonus
            await Transaction.create({
                user_id: referrer.user_id,
                transaction_type: 'referral_bonus',
                amount: bonusAmount,
                balance_type: 'bonus_balance',
                transaction_status: 'completed',
                description: `Referral bonus for referring ${newUser.user_name} who made first investment`,
                reference_id: investment.investment_id,
                reference_type: 'investment',
                source_user_id: newUser.user_id,
                processed_at: new Date()
            }, { transaction });

            await transaction.commit();
            console.log('✅ Investment and referral bonus processed successfully');

        } catch (error) {
            await transaction.rollback();
            throw error;
        }

        // Test 4: Verify results
        console.log('\n4️⃣ Verifying results...');
        
        // Check referrer wallet
        const updatedReferrerWallet = await Wallet.findOne({ where: { user_id: referrer.user_id } });
        console.log(`Referrer bonus balance: ${updatedReferrerWallet.bonus_balance} (should be 10.00)`);
        
        // Check new user wallet
        const updatedNewUserWallet = await Wallet.findOne({ where: { user_id: newUser.user_id } });
        console.log(`New user bonus balance: ${updatedNewUserWallet.bonus_balance} (should be 0.00)`);
        console.log(`New user main balance: ${updatedNewUserWallet.balance} (should be 400.00)`);

        // Check bonus records
        const bonusCount = await Bonus.count({
            where: {
                [Op.or]: [
                    { user_id: referrer.user_id },
                    { user_id: newUser.user_id }
                ]
            }
        });
        console.log(`Total bonus records created: ${bonusCount} (should be 1 - only for referrer)`);

        // Check transaction records
        const transactionCount = await Transaction.count({
            where: {
                [Op.or]: [
                    { user_id: referrer.user_id },
                    { user_id: newUser.user_id }
                ]
            }
        });
        console.log(`Total transaction records created: ${transactionCount} (should be 2 - investment + referral bonus)`);

        // Test 5: Verify specific conditions
        console.log('\n5️⃣ Verifying specific conditions...');
        
        // Verify only referrer got bonus
        const referrerBonus = await Bonus.findOne({
            where: { user_id: referrer.user_id, bonus_type: 'referral_bonus_referrer' }
        });
        
        const newUserBonus = await Bonus.findOne({
            where: { user_id: newUser.user_id, bonus_type: 'referral_bonus_referee' }
        });

        console.log(`✅ Referrer got bonus: ${referrerBonus ? 'YES' : 'NO'} (should be YES)`);
        console.log(`✅ New user got bonus: ${newUserBonus ? 'YES' : 'NO'} (should be NO)`);
        
        if (referrerBonus && referrerBonus.amount == 10) {
            console.log(`✅ Referrer bonus amount correct: ${referrerBonus.amount} points`);
        } else {
            console.log(`❌ Referrer bonus amount incorrect: ${referrerBonus?.amount || 0} points`);
        }

        console.log('\n🎉 Test completed successfully!');
        console.log('\n📊 Summary:');
        console.log('- Only referrer receives 10 points bonus ✅');
        console.log('- New user receives no bonus ✅');
        console.log('- All transactions are properly stored ✅');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.error(error.stack);
    } finally {
        // Clean up test data
        await cleanupTestData();
        console.log('\n🧹 Test data cleaned up');
    }
}

async function cleanupTestData() {
    try {
        // Delete in correct order to avoid foreign key constraints
        const testUserIds = await getUserIds();

        if (testUserIds.length > 0) {
            await Transaction.destroy({ where: { user_id: { [Op.in]: testUserIds } } });
            await Bonus.destroy({ where: { user_id: { [Op.in]: testUserIds } } });
            await Investment.destroy({ where: { user_id: { [Op.in]: testUserIds } } });
            await Wallet.destroy({ where: { user_id: { [Op.in]: testUserIds } } });
        }

        await Plan.destroy({ where: { name: 'Test Plan' } });
        await User.destroy({ where: { email: { [Op.in]: ['<EMAIL>', '<EMAIL>'] } } });
    } catch (error) {
        // Ignore cleanup errors
        console.log('Cleanup error (ignored):', error.message);
    }
}

async function getUserIds() {
    const users = await User.findAll({
        where: { user_name: { [Op.like]: 'test_%' } },
        attributes: ['user_id']
    });
    return users.map(u => u.user_id);
}

// Run the test
if (require.main === module) {
    testReferralSystem().then(() => {
        process.exit(0);
    }).catch((error) => {
        console.error('Test execution failed:', error);
        process.exit(1);
    });
}

module.exports = { testReferralSystem };
