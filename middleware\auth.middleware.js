const jwt = require('jsonwebtoken');

exports.verifyToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];

    // Check for Bearer token
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(403).json({ message: 'Authorization token is missing or malformed' });
    }

    const token = authHeader.split(' ')[1];

    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'mysecretkey');
        req.user = decoded; // { id: ..., user_id: ..., user_name: ..., email: ..., role: ... }

        // Check if user has user role
        if (decoded.role !== 'user') {
            return res.status(403).json({ message: 'Access denied. User role required.' });
        }

        next();
    } catch (err) {
        console.error('JWT verification error:', err.message);
        return res.status(401).json({ message: 'Invalid or expired token' });
    }
};

exports.verifyAdmin = (req, res, next) => {
    // First verify the token
    const authHeader = req.headers['authorization'];

    // Check for Bearer token
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(403).json({ message: 'Authorization token is missing or malformed' });
    }

    const token = authHeader.split(' ')[1];

    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'mysecretkey');
        req.user = decoded; // { id: ..., user_id: ..., user_name: ..., email: ..., role: ... }

        // Check if user has admin role
        if (decoded.role !== 'admin') {
            return res.status(403).json({ message: 'Access denied. Admin role required.' });
        }

        next();
    } catch (err) {
        console.error('JWT verification error:', err.message);
        return res.status(401).json({ message: 'Invalid or expired token' });
    }
};
