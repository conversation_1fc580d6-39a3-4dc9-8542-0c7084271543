const express = require('express');
const router = express.Router();
const { 
    buyPlan, 
    getUserInvestments, 
    getInvestmentDetails 
} = require('../controllers/investment.controller');
const { verifyToken } = require('../middleware/auth.middleware');

// All investment routes require authentication
router.post('/buy-plan', verifyToken, buyPlan);
router.get('/', verifyToken, getUserInvestments);
router.get('/:investmentId', verifyToken, getInvestmentDetails);

module.exports = router;
