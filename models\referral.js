'use strict';
module.exports = (sequelize, DataTypes) => {
  const Referral = sequelize.define('Referral', {
    referral_id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4
    },
    referrer_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: { model: 'user', key: 'user_id' }
    },
    referred_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: { model: 'user', key: 'user_id' }
    },
    level: { type: DataTypes.INTEGER, allowNull: false },
    status: { type: DataTypes.ENUM('active', 'inactive'), defaultValue: 'active' }
  }, {
    modelName: 'Referral',
    tableName: 'referral'
  });

  Referral.associate = (models) => {
    Referral.belongsTo(models.User, { foreignKey: 'referrer_id', as: 'referrer' });
    Referral.belongsTo(models.User, { foreignKey: 'referred_id', as: 'referred' });
  };

  Referral.createReferralTree = async function (referredId, referralCode, transaction) {
    const { User, Referral } = sequelize.models;
    const referrer = await User.findOne({ where: { referral_code: referralCode }, transaction });
    if (!referrer) throw new Error('Invalid referral code');

    await Referral.create({ referrer_id: referrer.user_id, referred_id: referredId, level: 1 }, { transaction });

    if (referrer.referred_by) {
      await Referral.create({ referrer_id: referrer.referred_by, referred_id: referredId, level: 2 }, { transaction });

      const grandRef = await User.findByPk(referrer.referred_by, { transaction });
      if (grandRef?.referred_by) {
        await Referral.create({ referrer_id: grandRef.referred_by, referred_id: referredId, level: 3 }, { transaction });
      }
    }
  };

  return Referral;
};