// utils/BaseController.js
class BaseController {
    static sendResponse(res, result, message = 'Success') {
        return res.status(200).json({
            success: true,
            data: result,
            message: message
        });
    }

    static sendError(res, error = 'Error', errorMessages = []) {
        const response = {
            success: false,
            message: error
        };

        if (errorMessages.length > 0) {
            response.data = errorMessages;
        }

        return res.status(200).json(response); // ✅ same 200 status like your Laravel style
    }
}

module.exports = BaseController;
