const { Plan } = require('../models');
const BaseController = require('../helpers/BaseController');
const { Op } = require('sequelize');
const { createPlanSchema, updatePlanSchema, planIdSchema } = require('../validations/plan.validation');

// Admin-only: Create a new plan
exports.createPlan = async (req, res) => {
    try {
        // Validate input
        const { error, value } = createPlanSchema.validate(req.body, { abortEarly: false });

        if (error) {
            const firstError = error.details[0];
            return BaseController.sendError(res, firstError.message);
        }

        // Check if plan name already exists
        const existingPlan = await Plan.findOne({ 
            where: { name: value.name } 
        });

        if (existingPlan) {
            return BaseController.sendError(res, 'Plan name already exists');
        }

        // Create new plan
        const newPlan = await Plan.create(value);

        return BaseController.sendResponse(res, {
            plan_id: newPlan.plan_id,
            name: newPlan.name,
            description: newPlan.description,
            amount: parseFloat(newPlan.amount),
            duration: newPlan.duration,
            return_percentage: parseFloat(newPlan.return_percentage),
            extra_return_percentage: parseFloat(newPlan.extra_return_percentage),
            investment_amount: parseFloat(newPlan.investment_amount),
            is_active: newPlan.is_active,
            createdAt: newPlan.createdAt,
            updatedAt: newPlan.updatedAt
        }, 'Plan created successfully');

    } catch (error) {
        console.error('Create plan error:', error);
        return BaseController.sendError(res, 'Failed to create plan', [{ error: error.message }]);
    }
};

// Admin-only: Get all plans (active and inactive)
exports.getAllPlans = async (req, res) => {
    try {
        const plans = await Plan.findAll({
            order: [['createdAt', 'DESC']]
        });

        // Format the response data
        const formattedPlans = plans.map(plan => ({
            plan_id: plan.plan_id,
            name: plan.name,
            description: plan.description,
            amount: parseFloat(plan.amount),
            duration: plan.duration,
            return_percentage: parseFloat(plan.return_percentage),
            extra_return_percentage: parseFloat(plan.extra_return_percentage),
            investment_amount: parseFloat(plan.investment_amount),
            is_active: plan.is_active,
            createdAt: plan.createdAt,
            updatedAt: plan.updatedAt
        }));

        return BaseController.sendResponse(res, formattedPlans, 'Plans fetched successfully');
    } catch (error) {
        console.error('Get all plans error:', error);
        return BaseController.sendError(res, 'Failed to fetch plans', [{ error: error.message }]);
    }
};

// Admin-only: Get plan by ID
exports.getPlanById = async (req, res) => {
    try {
        // Validate plan ID
        const { error, value } = planIdSchema.validate(req.params, { abortEarly: false });

        if (error) {
            const firstError = error.details[0];
            return BaseController.sendError(res, firstError.message);
        }

        const { planId } = value;
        const plan = await Plan.findByPk(planId);

        if (!plan) {
            return BaseController.sendError(res, 'Plan not found');
        }

        return BaseController.sendResponse(res, {
            plan_id: plan.plan_id,
            name: plan.name,
            description: plan.description,
            amount: parseFloat(plan.amount),
            duration: plan.duration,
            return_percentage: parseFloat(plan.return_percentage),
            extra_return_percentage: parseFloat(plan.extra_return_percentage),
            investment_amount: parseFloat(plan.investment_amount),
            is_active: plan.is_active,
            createdAt: plan.createdAt,
            updatedAt: plan.updatedAt
        }, 'Plan details fetched successfully');

    } catch (error) {
        console.error('Get plan by ID error:', error);
        return BaseController.sendError(res, 'Failed to fetch plan details', [{ error: error.message }]);
    }
};

// Admin-only: Update plan
exports.updatePlan = async (req, res) => {
    try {
        // Validate plan ID
        const { error: idError, value: idValue } = planIdSchema.validate(req.params, { abortEarly: false });

        if (idError) {
            const firstError = idError.details[0];
            return BaseController.sendError(res, firstError.message);
        }

        // Validate update data
        const { error, value } = updatePlanSchema.validate(req.body, { abortEarly: false });

        if (error) {
            const firstError = error.details[0];
            return BaseController.sendError(res, firstError.message);
        }

        const { planId } = idValue;
        const plan = await Plan.findByPk(planId);

        if (!plan) {
            return BaseController.sendError(res, 'Plan not found');
        }

        // Check if name is being updated and already exists
        if (value.name && value.name !== plan.name) {
            const existingPlan = await Plan.findOne({
                where: {
                    name: value.name,
                    plan_id: { [Op.ne]: planId }
                }
            });

            if (existingPlan) {
                return BaseController.sendError(res, 'Plan name already exists');
            }
        }

        // Update plan
        await plan.update(value);

        return BaseController.sendResponse(res, {
            plan_id: plan.plan_id,
            name: plan.name,
            description: plan.description,
            amount: parseFloat(plan.amount),
            duration: plan.duration,
            return_percentage: parseFloat(plan.return_percentage),
            extra_return_percentage: parseFloat(plan.extra_return_percentage),
            investment_amount: parseFloat(plan.investment_amount),
            is_active: plan.is_active,
            createdAt: plan.createdAt,
            updatedAt: plan.updatedAt
        }, 'Plan updated successfully');

    } catch (error) {
        console.error('Update plan error:', error);
        return BaseController.sendError(res, 'Failed to update plan', [{ error: error.message }]);
    }
};

// Admin-only: Delete plan (soft delete by setting is_active to false)
exports.deletePlan = async (req, res) => {
    try {
        // Validate plan ID
        const { error, value } = planIdSchema.validate(req.params, { abortEarly: false });

        if (error) {
            const firstError = error.details[0];
            return BaseController.sendError(res, firstError.message);
        }

        const { planId } = value;
        const plan = await Plan.findByPk(planId);

        if (!plan) {
            return BaseController.sendError(res, 'Plan not found');
        }

        // Soft delete by setting is_active to false
        await plan.update({ is_active: false });

        return BaseController.sendResponse(res, null, 'Plan deleted successfully');

    } catch (error) {
        console.error('Delete plan error:', error);
        return BaseController.sendError(res, 'Failed to delete plan', [{ error: error.message }]);
    }
};

// Public: Get all active plans (for users)
exports.getAllActivePlans = async (req, res) => {
    try {
        const activePlans = await Plan.findAll({
            where: { is_active: true },
            order: [['createdAt', 'DESC']]
        });

        // Format the response data
        const formattedPlans = activePlans.map(plan => ({
            plan_id: plan.plan_id,
            name: plan.name,
            description: plan.description,
            amount: parseFloat(plan.amount),
            duration: plan.duration,
            return_percentage: parseFloat(plan.return_percentage),
            extra_return_percentage: parseFloat(plan.extra_return_percentage),
            investment_amount: parseFloat(plan.investment_amount),
            createdAt: plan.createdAt
        }));

        return BaseController.sendResponse(res, formattedPlans, 'Active plans fetched successfully');
    } catch (error) {
        console.error('Get active plans error:', error);
        return BaseController.sendError(res, 'Failed to fetch active plans', [{ error: error.message }]);
    }
};
