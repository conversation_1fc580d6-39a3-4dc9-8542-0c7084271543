const Joi = require('joi');

const createPlanSchema = Joi.object({
    name: Joi.string().min(2).max(100).required().messages({
        'string.base': 'Name must be a string',
        'string.empty': 'Name is required',
        'string.min': 'Name should have at least 2 characters',
        'string.max': 'Name should not exceed 100 characters',
        'any.required': 'Name is required'
    }),
    description: Joi.string().min(10).max(1000).required().messages({
        'string.base': 'Description must be a string',
        'string.empty': 'Description is required',
        'string.min': 'Description should have at least 10 characters',
        'string.max': 'Description should not exceed 1000 characters',
        'any.required': 'Description is required'
    }),
    amount: Joi.number().positive().precision(2).required().messages({
        'number.base': 'Amount must be a number',
        'number.positive': 'Amount must be greater than 0',
        'any.required': 'Amount is required'
    }),
    duration: Joi.number().integer().min(1).required().messages({
        'number.base': 'Duration must be a number',
        'number.integer': 'Duration must be an integer',
        'number.min': 'Duration must be at least 1 day',
        'any.required': 'Duration is required'
    }),
    return_percentage: Joi.number().min(0).max(100).precision(2).required().messages({
        'number.base': 'Return percentage must be a number',
        'number.min': 'Return percentage cannot be negative',
        'number.max': 'Return percentage cannot exceed 100%',
        'any.required': 'Return percentage is required'
    }),
    extra_return_percentage: Joi.number().min(0).max(100).precision(2).optional().default(0).messages({
        'number.base': 'Extra return percentage must be a number',
        'number.min': 'Extra return percentage cannot be negative',
        'number.max': 'Extra return percentage cannot exceed 100%'
    }),
    investment_amount: Joi.number().positive().precision(2).required().messages({
        'number.base': 'Investment amount must be a number',
        'number.positive': 'Investment amount must be greater than 0',
        'any.required': 'Investment amount is required'
    }),
    is_active: Joi.boolean().optional().default(true).messages({
        'boolean.base': 'Is active must be a boolean value'
    })
});

const updatePlanSchema = Joi.object({
    name: Joi.string().min(2).max(100).optional().messages({
        'string.base': 'Name must be a string',
        'string.empty': 'Name cannot be empty',
        'string.min': 'Name should have at least 2 characters',
        'string.max': 'Name should not exceed 100 characters'
    }),
    description: Joi.string().min(10).max(1000).optional().messages({
        'string.base': 'Description must be a string',
        'string.empty': 'Description cannot be empty',
        'string.min': 'Description should have at least 10 characters',
        'string.max': 'Description should not exceed 1000 characters'
    }),
    amount: Joi.number().positive().precision(2).optional().messages({
        'number.base': 'Amount must be a number',
        'number.positive': 'Amount must be greater than 0'
    }),
    duration: Joi.number().integer().min(1).optional().messages({
        'number.base': 'Duration must be a number',
        'number.integer': 'Duration must be an integer',
        'number.min': 'Duration must be at least 1 day'
    }),
    return_percentage: Joi.number().min(0).max(100).precision(2).optional().messages({
        'number.base': 'Return percentage must be a number',
        'number.min': 'Return percentage cannot be negative',
        'number.max': 'Return percentage cannot exceed 100%'
    }),
    extra_return_percentage: Joi.number().min(0).max(100).precision(2).optional().messages({
        'number.base': 'Extra return percentage must be a number',
        'number.min': 'Extra return percentage cannot be negative',
        'number.max': 'Extra return percentage cannot exceed 100%'
    }),
    investment_amount: Joi.number().positive().precision(2).optional().messages({
        'number.base': 'Investment amount must be a number',
        'number.positive': 'Investment amount must be greater than 0'
    }),
    is_active: Joi.boolean().optional().messages({
        'boolean.base': 'Is active must be a boolean value'
    })
});

const planIdSchema = Joi.object({
    planId: Joi.string().uuid().required().messages({
        'string.base': 'Plan ID must be a string',
        'string.empty': 'Plan ID is required',
        'string.uuid': 'Plan ID must be a valid UUID',
        'any.required': 'Plan ID is required'
    })
});

module.exports = {
    createPlanSchema,
    updatePlanSchema,
    planIdSchema
};
