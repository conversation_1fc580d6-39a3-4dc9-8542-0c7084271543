'use strict';
const { DataTypes } = require('sequelize');
const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');

module.exports = (sequelize) => {
  const User = sequelize.define('User', {
    user_id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: { notEmpty: true, len: [2, 100] }
    },
    email: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      validate: { isEmail: true }
    },
    mobile: {
      type: DataTypes.STRING(15),
      allowNull: false,
      validate: { notEmpty: true }
    },
    user_name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: true,
        len: [3, 50],
        is: /^[a-zA-Z0-9_]+$/  // Only alphanumeric and underscore
      }
    },
    password: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: { len: [6, 255] }
    },
    referral_code: {
      type: DataTypes.STRING(10),
      allowNull: false,
      unique: true
    },
    referred_by: {
      type: DataTypes.UUID,
      allowNull: true,
      references: { model: 'user', key: 'user_id' },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE'
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    role: {
      type: DataTypes.ENUM('user', 'admin'),
      defaultValue: 'user'
    }
  }, {
    tableName: 'user',
    timestamps: true,
    hooks: {
      beforeCreate: async (user) => {
        user.password = await bcrypt.hash(user.password, 10);
        if (!user.referral_code) {
          user.referral_code = await generateUniqueReferralCode(User);
        }
      },
      beforeUpdate: async (user) => {
        if (user.changed('password')) {
          user.password = await bcrypt.hash(user.password, 10);
        }
      }
    }
  });

  User.associate = (models) => {
    User.belongsTo(models.User, { foreignKey: 'referred_by', as: 'referrer' });
    User.hasMany(models.User, { foreignKey: 'referred_by', as: 'referrals' });
    User.hasOne(models.Wallet, { foreignKey: 'user_id' });
    User.hasMany(models.Referral, { foreignKey: 'referrer_id', as: 'referrerRelations' });
    User.hasMany(models.Referral, { foreignKey: 'referred_id', as: 'referredRelations' });
    User.hasMany(models.Investment, { foreignKey: 'user_id', as: 'investments' });
    User.hasMany(models.Bonus, { foreignKey: 'user_id', as: 'bonuses' });
    User.hasMany(models.Bonus, { foreignKey: 'source_user_id', as: 'triggeredBonuses' });
  };

  User.prototype.verify_password = function (plainText) {
    return bcrypt.compare(plainText, this.password);
  };

  async function generateUniqueReferralCode(UserModel) {
    let code, exists = true;
    while (exists) {
      code = uuidv4().slice(0, 8).toUpperCase();
      exists = !!(await UserModel.findOne({ where: { referral_code: code } }));
    }
    return code;
  }

  return User;
};