const Joi = require('joi');

const buyPlanSchema = Joi.object({
    plan_id: Joi.string().uuid().required().messages({
        'string.base': 'Plan ID must be a string',
        'string.empty': 'Plan ID is required',
        'string.uuid': 'Plan ID must be a valid UUID',
        'any.required': 'Plan ID is required'
    }),
    payment_method: Joi.string().valid('wallet', 'external').optional().default('wallet').messages({
        'string.base': 'Payment method must be a string',
        'any.only': 'Payment method must be either wallet or external'
    }),
    transaction_id: Joi.string().max(100).optional().allow('').messages({
        'string.base': 'Transaction ID must be a string',
        'string.max': 'Transaction ID should not exceed 100 characters'
    })
});

const getInvestmentSchema = Joi.object({
    investmentId: Joi.string().uuid().required().messages({
        'string.base': 'Investment ID must be a string',
        'string.empty': 'Investment ID is required',
        'string.uuid': 'Investment ID must be a valid UUID',
        'any.required': 'Investment ID is required'
    })
});

const getUserInvestmentsSchema = Joi.object({
    status: Joi.string().valid('active', 'completed', 'cancelled', 'all').optional().default('all').messages({
        'string.base': 'Status must be a string',
        'any.only': 'Status must be one of: active, completed, cancelled, all'
    }),
    page: Joi.number().integer().min(1).optional().default(1).messages({
        'number.base': 'Page must be a number',
        'number.integer': 'Page must be an integer',
        'number.min': 'Page must be at least 1'
    }),
    limit: Joi.number().integer().min(1).max(100).optional().default(10).messages({
        'number.base': 'Limit must be a number',
        'number.integer': 'Limit must be an integer',
        'number.min': 'Limit must be at least 1',
        'number.max': 'Limit cannot exceed 100'
    })
});

module.exports = {
    buyPlanSchema,
    getInvestmentSchema,
    getUserInvestmentsSchema
};
