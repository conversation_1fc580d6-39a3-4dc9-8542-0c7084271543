# 🏆 Gold Trading Investment Platform

A complete **Node.js** investment platform where users can buy gold investment plans, earn referral bonuses, and manage their portfolios. Built with modern architecture, comprehensive validation, and robust security features.

## 📋 Table of Contents

- [What This Project Does](#what-this-project-does)
- [Tech Stack](#tech-stack)
- [Architecture Overview](#architecture-overview)
- [Database Structure](#database-structure)
- [Project Structure](#project-structure)
- [Getting Started](#getting-started)
- [Environment Configuration](#environment-configuration)
- [Authentication & Authorization](#authentication--authorization)
- [Business Logic](#business-logic)
- [Testing](#testing)
- [Deployment](#deployment)
- [Contributing](#contributing)

---

## 🎯 What This Project Does

Think of this as a **digital gold investment app** like Paytm Gold or PhonePe Gold:

### For Users:

- 👤 **Register & Login** with username/password authentication
- 💰 **Buy Investment Plans** (Gold Starter, Silver Premium, Platinum Elite, etc.)
- 🎁 **Get Referral Bonuses** (10% when you refer friends + welcome bonus)
- 📊 **Track Investments** (view active/completed/cancelled plans)
- 💳 **Wallet System** (balance management, bonus tracking)
- 🔍 **Profile Management** (view profile, referral statistics)
- 📈 **Bonus History** (detailed transaction history and summaries)

### For Admins:

- 🛠️ **Plan Management** (create, edit, delete, activate/deactivate investment plans)
- 👥 **User Management** (view all users, search, update status)
- 🎁 **Bonus Oversight** (track all bonus transactions across platform)
- 📈 **Dashboard Analytics** (comprehensive business insights and statistics)
- 🔐 **Admin Authentication** (role-based access control)

---

## 🛠️ Tech Stack

| Technology     | Purpose          | Why We Use It               | Version |
| -------------- | ---------------- | --------------------------- | ------- |
| **Node.js**    | Backend Server   | Fast, JavaScript everywhere | v16+    |
| **Express.js** | Web Framework    | Simple API creation         | v5.1.0  |
| **MySQL**      | Database         | Reliable, structured data   | Latest  |
| **Sequelize**  | ORM              | Easy database operations    | v6.37.7 |
| **JWT**        | Authentication   | Secure user sessions        | v9.0.2  |
| **Bcrypt**     | Password Hashing | Secure password storage     | v6.0.0  |
| **Joi**        | Validation       | Input data validation       | v17.13.3|
| **UUID**       | ID Generation    | Unique identifiers          | v11.1.0 |
| **CORS**       | Cross-Origin     | API access control          | v2.8.5  |
| **Dotenv**     | Environment      | Configuration management    | v17.2.0 |
| **Jest**       | Testing          | Unit & integration tests    | v29.7.0 |
| **Supertest**  | API Testing      | HTTP assertion testing      | v7.1.3  |

---

## 🏗️ Architecture Overview

### MVC Pattern Implementation

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Routes      │───▶│   Controllers   │───▶│     Models      │
│  (API Endpoints)│    │ (Business Logic)│    │ (Data Layer)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Middleware    │    │   Validations   │    │    Database     │
│ (Auth, CORS)    │    │ (Joi Schemas)   │    │    (MySQL)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Key Architectural Features

- **Modular Design**: Separated concerns with dedicated folders
- **Middleware Chain**: Authentication, validation, error handling
- **Database Abstraction**: Sequelize ORM with model associations
- **Validation Layer**: Joi schemas for input sanitization
- **Helper Classes**: BaseController for consistent API responses
- **Security First**: JWT tokens, password hashing, role-based access

---

## 🗄️ Database Structure

### Complete Database Schema

#### 📁 User Table (`user`)
```sql
user_id          UUID PRIMARY KEY    -- Unique user identifier
name             VARCHAR(100)        -- Full name (2-100 chars)
user_name        VARCHAR(50) UNIQUE  -- Login username (3-50 chars, alphanumeric + _)
email            VARCHAR(100) UNIQUE -- Email address (validated)
mobile           VARCHAR(15)         -- Mobile number
password         VARCHAR(255)        -- Bcrypt hashed password
referral_code    VARCHAR(10) UNIQUE  -- Auto-generated referral code
referred_by      UUID                -- References user.user_id (who referred)
is_active        BOOLEAN DEFAULT true-- Account status
role             ENUM('user','admin') DEFAULT 'user' -- User role
createdAt        TIMESTAMP           -- Registration date
updatedAt        TIMESTAMP           -- Last update
```

#### 📁 Plan Table (`plan`)
```sql
plan_id                 UUID PRIMARY KEY    -- Unique plan identifier
name                    VARCHAR(100) UNIQUE -- Plan name (2-100 chars)
description             TEXT                -- Plan description (10-1000 chars)
amount                  DECIMAL(15,2)       -- Plan price (minimum 0.01)
duration                INTEGER             -- Duration in days (minimum 1)
return_percentage       DECIMAL(5,2)        -- Base return % (0-100)
extra_return_percentage DECIMAL(5,2)        -- Bonus return % (0-100)
investment_amount       DECIMAL(15,2)       -- Required investment amount
is_active               BOOLEAN DEFAULT true-- Plan availability
createdAt               TIMESTAMP           -- Creation date
updatedAt               TIMESTAMP           -- Last update
```

#### 📁 Investment Table (`investment`)
```sql
investment_id       UUID PRIMARY KEY    -- Unique investment identifier
user_id             UUID                -- References user.user_id
plan_id             UUID                -- References plan.plan_id
amount              DECIMAL(15,2)       -- Investment amount
start_date          DATE                -- Investment start date
end_date            DATE                -- Investment end date
status              ENUM('active','completed','cancelled') DEFAULT 'active'
payment_method      ENUM('wallet','external') DEFAULT 'wallet'
transaction_id      VARCHAR(100)        -- Transaction reference
is_first_investment BOOLEAN DEFAULT false -- For referral bonus tracking
expected_return     DECIMAL(15,2)       -- Calculated expected return
actual_return       DECIMAL(15,2)       -- Actual return when completed
createdAt           TIMESTAMP           -- Investment date
updatedAt           TIMESTAMP           -- Last update
```

#### 📁 Wallet Table (`wallet`)
```sql
wallet_id        UUID PRIMARY KEY    -- Unique wallet identifier
user_id          UUID UNIQUE         -- References user.user_id
balance          DECIMAL(15,2)       -- Available balance
bonus_balance    DECIMAL(15,2)       -- Referral bonus balance
total_deposited  DECIMAL(15,2)       -- Total money deposited
total_withdrawn  DECIMAL(15,2)       -- Total money withdrawn
total_invested   DECIMAL(15,2)       -- Total money invested
is_active        BOOLEAN DEFAULT true-- Wallet status
createdAt        TIMESTAMP           -- Creation date
updatedAt        TIMESTAMP           -- Last update
```

#### 📁 Bonus Table (`bonus`)
```sql
bonus_id            UUID PRIMARY KEY    -- Unique bonus identifier
user_id             UUID                -- References user.user_id (recipient)
bonus_type          ENUM(               -- Type of bonus
                      'referral_bonus_referrer',    -- 10% to referrer
                      'referral_bonus_referee',     -- 10% to new user
                      'investment_return',          -- Investment completion
                      'admin_bonus',                -- Manual admin bonus
                      'promotional_bonus'           -- Marketing bonus
                    )
amount              DECIMAL(15,2)       -- Bonus amount (minimum 0.01)
description         TEXT                -- Bonus description
status              ENUM('pending','completed','cancelled') DEFAULT 'completed'
source_user_id      UUID                -- References user.user_id (trigger user)
source_investment_id UUID               -- References investment.investment_id
processed_at        TIMESTAMP           -- When bonus was processed
admin_notes         TEXT                -- Admin notes
createdAt           TIMESTAMP           -- Bonus creation date
updatedAt           TIMESTAMP           -- Last update
```

#### 📁 Referral Table (`referral`)
```sql
referral_id     UUID PRIMARY KEY    -- Unique referral identifier
referrer_id     UUID                -- References user.user_id (who referred)
referred_id     UUID                -- References user.user_id (who was referred)
level           INTEGER             -- Referral level (1, 2, 3...)
status          ENUM('active','inactive') DEFAULT 'active' -- Referral status
createdAt       TIMESTAMP           -- Referral date
updatedAt       TIMESTAMP           -- Last update
```

### Database Relationships

```
User (1) ──────── (1) Wallet
User (1) ──────── (∞) Investment
User (1) ──────── (∞) Bonus (as recipient)
User (1) ──────── (∞) Bonus (as source)
User (1) ──────── (∞) Referral (as referrer)
User (1) ──────── (∞) Referral (as referred)
Plan (1) ──────── (∞) Investment
Investment (1) ── (∞) Bonus
```

### Indexes for Performance

```sql
-- User table indexes
CREATE INDEX idx_user_username ON user(user_name);
CREATE INDEX idx_user_email ON user(email);
CREATE INDEX idx_user_referral_code ON user(referral_code);
CREATE INDEX idx_user_referred_by ON user(referred_by);

-- Plan table indexes
CREATE INDEX idx_plan_active ON plan(is_active);
CREATE INDEX idx_plan_name ON plan(name);

-- Investment table indexes
CREATE INDEX idx_investment_user ON investment(user_id);
CREATE INDEX idx_investment_plan ON investment(plan_id);
CREATE INDEX idx_investment_status ON investment(status);
CREATE INDEX idx_investment_end_date ON investment(end_date);
CREATE INDEX idx_investment_first ON investment(is_first_investment);

-- Bonus table indexes
CREATE INDEX idx_bonus_user ON bonus(user_id);
CREATE INDEX idx_bonus_type ON bonus(bonus_type);
CREATE INDEX idx_bonus_status ON bonus(status);
CREATE INDEX idx_bonus_source_investment ON bonus(source_investment_id);
CREATE INDEX idx_bonus_created ON bonus(createdAt);
```

---

## 📁 Project Structure

```
gold-trading/
├── 📂 config/                    # Database configuration
│   └── config.js                 # Sequelize database config
├── 📂 controllers/               # Business logic layer
│   ├── admin.controller.js       # Admin management functions
│   ├── auth.controller.js        # Authentication logic
│   ├── bonus.controller.js       # Bonus management
│   ├── investment.controller.js  # Investment operations
│   ├── plan.controller.js        # Plan management
│   └── user.controller.js        # User profile operations
├── 📂 helpers/                   # Utility classes
│   └── BaseController.js         # Standardized API responses
├── 📂 middleware/                # Express middleware
│   └── auth.middleware.js        # JWT authentication & authorization
├── 📂 models/                    # Database models (Sequelize)
│   ├── index.js                  # Model loader & associations
│   ├── user.js                   # User model with hooks
│   ├── plan.js                   # Investment plan model
│   ├── investment.js             # Investment tracking model
│   ├── wallet.js                 # User wallet model
│   ├── bonus.js                  # Bonus transaction model
│   └── referral.js               # Referral relationship model
├── 📂 routes/                    # API route definitions
│   ├── auth.route.js             # Authentication endpoints
│   ├── user.routes.js            # User profile endpoints
│   ├── admin.routes.js           # Admin management endpoints
│   ├── plan.routes.js            # Plan viewing endpoints
│   ├── investment.routes.js      # Investment operations
│   └── bonus.routes.js           # Bonus history endpoints
├── 📂 validations/               # Input validation schemas
│   ├── auth.validation.js        # Registration & login validation
│   ├── investment.validation.js  # Investment operation validation
│   └── plan.validation.js        # Plan management validation
├── 📂 seeders/                   # Database seed files
│   ├── **************-create-admin-user.js  # Default admin account
│   └── **************-create-plans.js       # Sample investment plans
├── 📂 tests/                     # Test files (Jest)
│   └── setup.js                  # Test configuration
├── 📄 server.js                  # Main application entry point
├── 📄 package.json               # Dependencies & scripts
├── 📄 jest.config.js             # Testing configuration
└── 📄 .env                       # Environment variables (create this)
```

### Key Architecture Decisions

- **Controllers**: Handle business logic and orchestrate model interactions
- **Models**: Define database schema with Sequelize ORM and associations
- **Routes**: Define API endpoints with middleware chains
- **Validations**: Joi schemas for input sanitization and validation
- **Middleware**: JWT authentication and role-based authorization
- **Helpers**: Standardized response formatting and utility functions

---

## 🚀 Getting Started

### Prerequisites

- **Node.js** (v16 or higher) - [Download](https://nodejs.org/)
- **MySQL** (v8.0 or higher) - [Download](https://dev.mysql.com/downloads/)
- **Git** - [Download](https://git-scm.com/)
- **Postman** (optional) - For API testing

### Installation Steps

#### 1. **Clone the repository**

```bash
git clone <your-repo-url>
cd gold-trading
```

#### 2. **Install dependencies**

```bash
npm install
```

This installs all required packages:
- Express.js for web framework
- Sequelize for database ORM
- JWT for authentication
- Bcrypt for password hashing
- Joi for validation
- Jest for testing

#### 3. **Setup MySQL Database**

```bash
# Login to MySQL
mysql -u root -p

# Create database
CREATE DATABASE gold;

# Create user (optional, for security)
CREATE USER 'gold_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON gold.* TO 'gold_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

#### 4. **Environment Configuration**

Create `.env` file in project root:

```env
# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_mysql_password
DB_NAME=gold
DB_DIALECT=mysql

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random

# Optional: For production
# DB_SSL=true
# DB_POOL_MAX=5
# DB_POOL_MIN=0
# DB_POOL_ACQUIRE=30000
# DB_POOL_IDLE=10000
```

#### 5. **Database Setup & Seeding**

```bash
# Start the server (creates tables automatically)
npm run dev

# In another terminal, seed initial data
npm run seed
```

This creates:
- **Admin account**: `adminuser` / `Admin1234@`
- **Sample plans**: Gold Starter, Silver Premium, Platinum Elite, etc.

#### 6. **Verify Installation**

```bash
# Test server is running
curl http://localhost:3000
# Should return: "API is running ✅"

# Test database connection
# Check server logs for: "✅ Database connection established successfully"
```

#### 7. **Available Scripts**

```bash
npm start          # Production server
npm run dev        # Development server with nodemon
npm run seed       # Seed database with initial data
npm test           # Run Jest tests
npm test:watch     # Run tests in watch mode
```

Server will run on: `http://localhost:3000`

---

## ⚙️ Environment Configuration

### Required Environment Variables

| Variable | Description | Example | Required |
|----------|-------------|---------|----------|
| `PORT` | Server port | `3000` | No (default: 3000) |
| `NODE_ENV` | Environment | `development` | No |
| `DB_HOST` | MySQL host | `localhost` | Yes |
| `DB_USER` | MySQL username | `root` | Yes |
| `DB_PASSWORD` | MySQL password | `password123` | Yes |
| `DB_NAME` | Database name | `gold` | Yes |
| `DB_DIALECT` | Database type | `mysql` | No (default: mysql) |
| `JWT_SECRET` | JWT signing key | `your_secret_key` | Yes |

### Optional Environment Variables

| Variable | Description | Default | Purpose |
|----------|-------------|---------|---------|
| `DB_SSL` | Enable SSL | `false` | Production security |
| `DB_POOL_MAX` | Max connections | `5` | Connection pooling |
| `DB_POOL_MIN` | Min connections | `0` | Connection pooling |
| `DB_POOL_ACQUIRE` | Acquire timeout | `30000` | Connection timeout |
| `DB_POOL_IDLE` | Idle timeout | `10000` | Connection cleanup |

### Environment-Specific Configurations

#### Development (.env.development)
```env
NODE_ENV=development
PORT=3000
DB_HOST=localhost
JWT_SECRET=dev_secret_key
```

#### Production (.env.production)
```env
NODE_ENV=production
PORT=8080
DB_HOST=your_production_host
DB_SSL=true
JWT_SECRET=your_super_secure_production_key
```

#### Testing (.env.test)
```env
NODE_ENV=test
DB_NAME=gold_test
JWT_SECRET=test_secret_key
```

---

## 🔐 Authentication & Authorization

### JWT Token Structure

The JWT token contains the following payload:
```json
{
  "id": "user_uuid",
  "user_id": "user_uuid",
  "user_name": "johndoe123",
  "email": "<EMAIL>",
  "role": "user|admin",
  "iat": 1642781234,
  "exp": 1642867634
}
```

### Middleware Chain

1. **verifyToken**: Validates JWT token for authenticated routes
2. **verifyAdmin**: Validates JWT token + checks admin role for admin routes

### Role-Based Access Control

| Route Pattern | Access Level | Middleware |
|---------------|--------------|------------|
| `/api/register`, `/api/login` | Public | None |
| `/api/user/*` | Authenticated Users | `verifyToken` |
| `/api/plans` | Authenticated Users | `verifyToken` |
| `/api/investments/*` | Authenticated Users | `verifyToken` |
| `/api/bonuses/*` | Authenticated Users | `verifyToken` |
| `/api/admin/*` | Admin Only | `verifyAdmin` |

---

## 🔄 Business Logic & Workflows

### 1. User Registration & Referral System

```mermaid
graph TD
    A[User Registration] --> B{Referral Code Provided?}
    B -->|Yes| C[Validate Referral Code]
    B -->|No| D[Create User Account]
    C -->|Valid| E[Link to Referrer]
    C -->|Invalid| F[Registration Failed]
    E --> G[Create User Account]
    D --> H[Generate Referral Code]
    G --> H
    H --> I[Create Wallet]
    I --> J[Create Referral Tree]
    J --> K[Registration Complete]
```

**Process:**
1. User provides registration details + optional referral code
2. System validates all inputs (email uniqueness, username format, etc.)
3. If referral code provided, validates it exists and is active
4. Creates user account with bcrypt-hashed password
5. Generates unique referral code for new user
6. Creates wallet with zero balance
7. If referred, creates referral relationship in referral table
8. Returns JWT token for immediate login

### 2. Investment Purchase Flow

```mermaid
graph TD
    A[Select Investment Plan] --> B[Check User Authentication]
    B --> C{Has Active Investment?}
    C -->|Yes| D[Block Purchase]
    C -->|No| E[Check Wallet Balance]
    E -->|Insufficient| F[Purchase Failed]
    E -->|Sufficient| G[Deduct from Wallet]
    G --> H[Create Investment Record]
    H --> I{First Investment?}
    I -->|Yes| J{Has Referrer?}
    I -->|No| K[Purchase Complete]
    J -->|Yes| L[Process Referral Bonuses]
    J -->|No| K
    L --> M[10% Bonus to Referrer]
    M --> N[10% Welcome Bonus to User]
    N --> K
```

**Business Rules:**
- Users can only have ONE active investment at a time
- Wallet balance must be sufficient for plan amount
- First investment triggers referral bonuses (if user was referred)
- Referral bonus = 10% of investment amount to both referrer and referee
- Investment duration calculated from current date
- Expected return = investment + (investment × (base_return + extra_return) / 100)

### 3. Referral Bonus Calculation

```javascript
// Referral bonus logic (from investment.controller.js)
if (isFirstInvestment && user.referred_by) {
    const bonusAmount = investmentAmount * 0.10; // 10%

    // Bonus to referrer
    await Bonus.create({
        user_id: user.referred_by,
        bonus_type: 'referral_bonus_referrer',
        amount: bonusAmount,
        description: `Referral bonus for referring ${user.user_name}`,
        source_user_id: user_id,
        source_investment_id: newInvestment.investment_id
    });

    // Welcome bonus to referee
    await Bonus.create({
        user_id: user_id,
        bonus_type: 'referral_bonus_referee',
        amount: bonusAmount,
        description: `Welcome bonus for first investment`,
        source_user_id: user.referred_by,
        source_investment_id: newInvestment.investment_id
    });
}
```

### 4. Database Transaction Safety

All critical operations use database transactions:
- User registration (user + wallet + referral creation)
- Investment purchase (wallet deduction + investment creation + bonus processing)
- Plan management (creation/updates with validation)

### 5. Investment Status Management

| Status | Description | Can Purchase New? |
|--------|-------------|-------------------|
| `active` | Investment is running | ❌ No |
| `completed` | Investment finished, returns paid | ✅ Yes |
| `cancelled` | Investment cancelled by admin | ✅ Yes |

### 6. Wallet Balance Types

| Balance Type | Purpose | Source |
|--------------|---------|--------|
| `balance` | Main investment balance | Deposits, returns |
| `bonus_balance` | Referral bonuses | Referral system |
| `total_deposited` | Lifetime deposits | External deposits |
| `total_withdrawn` | Lifetime withdrawals | Withdrawal requests |
| `total_invested` | Lifetime investments | Investment purchases |

---

## 🧪 Testing

### Test Setup

The project uses **Jest** for testing with **Supertest** for API testing.

#### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm test -- --coverage
```

### Test Structure

```
tests/
├── setup.js              # Test configuration
├── auth.test.js          # Authentication tests
├── investment.test.js    # Investment flow tests
├── bonus.test.js         # Bonus system tests
└── admin.test.js         # Admin functionality tests
```

### Sample Test Data

After running `npm run seed`, you'll have:

**Admin Account:**
- Username: `adminuser`
- Password: `Admin1234@`

**Sample Plans:**
- Gold Starter Plan (₹1,000, 30 days, 5.5% return)
- Silver Premium Plan (₹5,000, 90 days, 8.75% return)
- Platinum Elite Plan (₹10,000, 180 days, 12.5% return)
- Diamond VIP Plan (₹25,000, 365 days, 18% return)

### Quick API Testing

For quick testing via terminal:
```bash
# Test server is running
curl http://localhost:3000

# Test user registration
curl -X POST http://localhost:3000/api/register \
  -H "Content-Type: application/json" \
  -d '{"name":"Test User","user_name":"testuser123","email":"<EMAIL>","mobile":"**********","password":"Test1234@","confirm_password":"Test1234@"}'

# Test login (replace with actual credentials)
curl -X POST http://localhost:3000/api/login \
  -H "Content-Type: application/json" \
  -d '{"user_name":"testuser123","password":"Test1234@"}'
```

---

## 🚀 Deployment

### Production Checklist

- [ ] Set strong `JWT_SECRET` in production
- [ ] Configure production database with SSL
- [ ] Set up environment variables securely
- [ ] Enable CORS for your frontend domain
- [ ] Set up proper logging
- [ ] Configure rate limiting
- [ ] Set up monitoring and health checks

### Environment Variables for Production

```env
NODE_ENV=production
PORT=8080
DB_HOST=your_production_host
DB_SSL=true
JWT_SECRET=your_super_secure_production_key
```

---

## 🤝 Contributing

### Development Workflow

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Make your changes
4. Add tests for new functionality
5. Run tests: `npm test`
6. Commit changes: `git commit -m 'Add new feature'`
7. Push to branch: `git push origin feature/new-feature`
8. Submit a pull request

### Code Style

- Use ES6+ features
- Follow consistent naming conventions
- Add JSDoc comments for functions
- Maintain test coverage above 80%

---

## 🎯 Key Features

✅ **User Management** - Registration, login, profiles
✅ **Investment System** - Buy plans, track investments
✅ **Referral Program** - 10% bonus system
✅ **Wallet System** - Balance management
✅ **Admin Panel** - Plan & user management
✅ **Bonus Tracking** - Complete audit trail
✅ **Security** - JWT auth, password hashing
✅ **Validation** - Input sanitization
✅ **Testing** - Comprehensive test suite
✅ **Documentation** - Complete API documentation

---

## 🚀 Ready to Use!

Your gold trading investment platform is ready! Users can register, invest in plans, earn referral bonuses, and admins can manage everything through APIs.

**Happy Coding!** 💻✨
