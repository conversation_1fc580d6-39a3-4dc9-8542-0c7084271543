'use strict';
const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Transaction = sequelize.define('Transaction', {
    transaction_id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
      allowNull: false
    },
    user_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: { 
        model: 'user', 
        key: 'user_id' 
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    },
    transaction_type: {
      type: DataTypes.ENUM(
        'investment',           // Plan purchase
        'referral_bonus',       // Referral bonus received
        'deposit',              // Money deposit to wallet
        'withdrawal',           // Money withdrawal from wallet
        'investment_return',    // Investment completion return
        'admin_bonus',          // Manual admin bonus
        'promotional_bonus'     // Promotional/marketing bonus
      ),
      allowNull: false
    },
    amount: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      validate: {
        min: 0.01
      }
    },
    balance_type: {
      type: DataTypes.ENUM('main_balance', 'bonus_balance'),
      allowNull: false,
      defaultValue: 'main_balance',
      comment: 'Which balance was affected'
    },
    transaction_status: {
      type: DataTypes.ENUM('pending', 'completed', 'failed', 'cancelled'),
      allowNull: false,
      defaultValue: 'completed'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    reference_id: {
      type: DataTypes.UUID,
      allowNull: true,
      comment: 'Reference to related record (investment_id, bonus_id, etc.)'
    },
    reference_type: {
      type: DataTypes.ENUM('investment', 'bonus', 'deposit', 'withdrawal'),
      allowNull: true,
      comment: 'Type of referenced record'
    },
    source_user_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: { 
        model: 'user', 
        key: 'user_id' 
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
      comment: 'User who triggered this transaction (for referral bonuses)'
    },
    payment_method: {
      type: DataTypes.ENUM('wallet', 'external', 'bonus'),
      allowNull: true,
      comment: 'Payment method used for the transaction'
    },
    external_transaction_id: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: 'External payment gateway transaction ID'
    },
    admin_notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    processed_at: {
      type: DataTypes.DATE,
      allowNull: true
    }
  }, {
    tableName: 'transaction',
    timestamps: true,
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['transaction_type']
      },
      {
        fields: ['transaction_status']
      },
      {
        fields: ['createdAt']
      },
      {
        fields: ['reference_id', 'reference_type']
      }
    ]
  });

  Transaction.associate = (models) => {
    Transaction.belongsTo(models.User, { foreignKey: 'user_id', as: 'user' });
    Transaction.belongsTo(models.User, { foreignKey: 'source_user_id', as: 'sourceUser' });
  };

  return Transaction;
};
