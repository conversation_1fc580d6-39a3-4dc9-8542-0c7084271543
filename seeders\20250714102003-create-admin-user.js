'use strict';
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const adminUserId = uuidv4(); // Generate UUID for admin user
    const hashedPassword = await bcrypt.hash('Admin1234@', 10);

    // Insert admin user
    await queryInterface.bulkInsert('user', [{
      user_id: adminUserId,
      name: 'Admin User',
      user_name: 'adminuser',
      email: '<EMAIL>',
      mobile: '9999999999',
      password: hashedPassword,
      referral_code: 'ADMIN001',
      referred_by: null,
      role: 'admin',
      is_active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }], {});

    // Insert wallet for admin user
    await queryInterface.bulkInsert('wallet', [{
      wallet_id: uuidv4(), // 👈 Add this line
      user_id: adminUserId,
      balance: 10000.00,
      bonus_balance: 0.00,
      total_deposited: 10000.00,
      total_withdrawn: 0.00,
      total_invested: 0.00,
      is_active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }], {});
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('wallet', {
      user_id: {
        [Sequelize.Op.in]: queryInterface.sequelize.literal(
          "(SELECT user_id FROM user WHERE email = '<EMAIL>')"
        )
      }
    }, {});

    await queryInterface.bulkDelete('user', { email: '<EMAIL>' }, {});
  }
};
