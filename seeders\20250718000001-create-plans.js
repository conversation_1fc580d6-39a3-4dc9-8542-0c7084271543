'use strict';
const { v4: uuidv4 } = require('uuid');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const plans = [
      {
        plan_id: uuidv4(),
        name: 'Gold Starter Plan',
        description: 'Perfect for beginners looking to start their gold investment journey. Low risk with steady returns over a short period.',
        amount: 1000.00,
        duration: 30,
        return_percentage: 5.50,
        extra_return_percentage: 1.00,
        investment_amount: 1000.00,
        is_active: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        plan_id: uuidv4(),
        name: 'Silver Premium Plan',
        description: 'Ideal for moderate investors seeking balanced growth. Medium risk with attractive returns over 3 months.',
        amount: 5000.00,
        duration: 90,
        return_percentage: 8.75,
        extra_return_percentage: 2.25,
        investment_amount: 5000.00,
        is_active: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        plan_id: uuidv4(),
        name: 'Platinum Elite Plan',
        description: 'Designed for serious investors who want maximum returns. Higher investment with premium benefits over 6 months.',
        amount: 10000.00,
        duration: 180,
        return_percentage: 12.50,
        extra_return_percentage: 3.50,
        investment_amount: 10000.00,
        is_active: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        plan_id: uuidv4(),
        name: 'Diamond VIP Plan',
        description: 'Exclusive plan for high-net-worth individuals. Premium gold investment with exceptional returns over 1 year.',
        amount: 25000.00,
        duration: 365,
        return_percentage: 18.00,
        extra_return_percentage: 5.00,
        investment_amount: 25000.00,
        is_active: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        plan_id: uuidv4(),
        name: 'Quick Return Plan',
        description: 'Short-term investment plan for quick profits. Ideal for those who want fast returns with minimal commitment.',
        amount: 2500.00,
        duration: 15,
        return_percentage: 3.25,
        extra_return_percentage: 0.75,
        investment_amount: 2500.00,
        is_active: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        plan_id: uuidv4(),
        name: 'Long Term Growth Plan',
        description: 'Perfect for long-term wealth building. Compound your gold investments over 2 years with maximum growth potential.',
        amount: 15000.00,
        duration: 730,
        return_percentage: 25.00,
        extra_return_percentage: 7.50,
        investment_amount: 15000.00,
        is_active: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        plan_id: uuidv4(),
        name: 'Legacy Plan (Inactive)',
        description: 'This is an old plan that is no longer available for new investments. Kept for historical records.',
        amount: 7500.00,
        duration: 120,
        return_percentage: 10.00,
        extra_return_percentage: 2.00,
        investment_amount: 7500.00,
        is_active: false,
        createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
        updatedAt: new Date()
      }
    ];

    await queryInterface.bulkInsert('plan', plans, {});
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('plan', null, {});
  }
};
