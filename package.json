{"name": "gold", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "migrate": "npx sequelize-cli db:migrate", "seed": "npx sequelize-cli db:seed:all", "test": "jest --detect<PERSON><PERSON><PERSON><PERSON><PERSON> --forceExit", "test:watch": "jest --watch --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"axios": "^1.10.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "form-data": "^4.0.4", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.2", "nodemailer": "^7.0.5", "nodemon": "^3.1.10", "sequelize": "^6.37.7", "sequelize-cli": "^6.6.3", "uuid": "^11.1.0"}, "devDependencies": {"jest": "^29.7.0", "supertest": "^7.1.3"}}