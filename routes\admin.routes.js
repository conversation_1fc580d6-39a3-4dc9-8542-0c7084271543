const express = require('express');
const router = express.Router();
const {
    getAllUsers,
    getUserById,
    updateUserStatus,
    getDashboardStats,
    searchUsers
} = require('../controllers/admin.controller');
const {
    createPlan,
    getAllPlans,
    getPlanById,
    updatePlan,
    deletePlan
} = require('../controllers/plan.controller');

const { getAllBonusTransactions } = require('../controllers/bonus.controller');
const { verifyAdmin } = require('../middleware/auth.middleware');

// All admin routes require admin role
router.get('/dashboard', verifyAdmin, getDashboardStats);
router.get('/users', verifyAdmin, getAllUsers);
router.get('/users/search', verifyAdmin, searchUsers);
router.get('/users/:userId', verifyAdmin, getUserById);
router.put('/users/:userId/status', verifyAdmin, updateUserStatus);

// Plan management routes
router.post('/plans', verifyAdmin, createPlan);
router.get('/plans', verifyAdmin, getAllPlans);
router.get('/plans/:planId', verifyAdmin, getPlanById);
router.put('/plans/:planId', verifyAdmin, updatePlan);
router.delete('/plans/:planId', verifyAdmin, deletePlan);

// Bonus management routes
router.get('/bonuses', verifyAdmin, getAllBonusTransactions);

module.exports = router;
