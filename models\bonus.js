'use strict';
const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Bonus = sequelize.define('Bonus', {
    bonus_id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
      allowNull: false
    },
    user_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: { 
        model: 'user', 
        key: 'user_id' 
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    },
    bonus_type: {
      type: DataTypes.ENUM(
        'referral_bonus_referrer',    // 10% bonus to referrer
        'referral_bonus_referee',     // 10% bonus to new user
        'investment_return',          // Investment completion bonus
        'admin_bonus',                // Manual admin bonus
        'promotional_bonus'           // Promotional/marketing bonus
      ),
      allowNull: false
    },
    amount: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      validate: {
        min: 0.01
      }
    },
    source_investment_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: { 
        model: 'investment', 
        key: 'investment_id' 
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
      comment: 'Investment that triggered this bonus (for referral bonuses)'
    },
    source_user_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: { 
        model: 'user', 
        key: 'user_id' 
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
      comment: 'User who triggered this bonus (for referral bonuses)'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    status: {
      type: DataTypes.ENUM('pending', 'completed', 'failed', 'cancelled'),
      allowNull: false,
      defaultValue: 'completed'
    },
    processed_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    admin_notes: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    tableName: 'bonus',
    timestamps: true,
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['bonus_type']
      },
      {
        fields: ['status']
      },
      {
        fields: ['source_investment_id']
      },
      {
        fields: ['createdAt']
      }
    ]
  });

  Bonus.associate = (models) => {
    Bonus.belongsTo(models.User, { 
      foreignKey: 'user_id',
      as: 'user'
    });
    Bonus.belongsTo(models.User, { 
      foreignKey: 'source_user_id',
      as: 'sourceUser'
    });
    Bonus.belongsTo(models.Investment, { 
      foreignKey: 'source_investment_id',
      as: 'sourceInvestment'
    });
  };

  // Helper method to create referral bonus transactions
  Bonus.createReferralBonus = async function(referrerUserId, refereeUserId, investmentId, bonusAmount, transaction) {
    const bonuses = [];

    // Create bonus for referrer (person who referred) - ONLY referrer gets bonus now
    const referrerBonus = await Bonus.create({
      user_id: referrerUserId,
      bonus_type: 'referral_bonus_referrer',
      amount: bonusAmount,
      source_investment_id: investmentId,
      source_user_id: refereeUserId,
      description: `Referral bonus for referring a new user who made their first investment of ₹${bonusAmount * 10}`,
      status: 'completed',
      processed_at: new Date()
    }, { transaction });

    bonuses.push(referrerBonus);

    // NOTE: Referee (new user) no longer gets bonus - only referrer gets 10 points

    return bonuses;
  };

  return Bonus;
};
