const { User, Wallet } = require('../models');
const BaseController = require('../helpers/BaseController');
const { Op } = require('sequelize');

exports.getAllUsers = async (req, res) => {
    try {
        const users = await User.findAll({
            attributes: ['user_id', 'name', 'user_name', 'email', 'mobile', 'role', 'is_active', 'createdAt'],
            include: [{
                model: Wallet,
                attributes: ['balance', 'bonus_balance', 'total_deposited', 'total_withdrawn', 'total_invested']
            }],
            order: [['createdAt', 'DESC']]
        });

        return BaseController.sendResponse(res, users, 'Users fetched successfully');
    } catch (error) {
        console.error('Get all users error:', error);
        return BaseController.sendError(res, 'Failed to fetch users', [{ error: error.message }]);
    }
};

exports.getUserById = async (req, res) => {
    try {
        const { userId } = req.params;

        const user = await User.findByPk(userId, {
            attributes: ['user_id', 'name', 'user_name', 'email', 'mobile', 'role', 'is_active', 'referral_code', 'createdAt'],
            include: [{
                model: Wallet,
                attributes: ['balance', 'bonus_balance', 'total_deposited', 'total_withdrawn', 'total_invested', 'is_active']
            }]
        });

        if (!user) {
            return BaseController.sendError(res, 'User not found', 404);
        }

        return BaseController.sendResponse(res, user, 'User details fetched successfully');
    } catch (error) {
        console.error('Get user by ID error:', error);
        return BaseController.sendError(res, 'Failed to fetch user details', [{ error: error.message }]);
    }
};

exports.updateUserStatus = async (req, res) => {
    try {
        const { userId } = req.params;
        const { is_active } = req.body;

        if (typeof is_active !== 'boolean') {
            return BaseController.sendError(res, 'is_active must be a boolean value');
        }

        const user = await User.findByPk(userId);
        if (!user) {
            return BaseController.sendError(res, 'User not found', 404);
        }

        // Prevent admin from deactivating themselves
        if (user.user_id === req.user.user_id && !is_active) {
            return BaseController.sendError(res, 'You cannot deactivate your own account');
        }

        await user.update({ is_active });

        return BaseController.sendResponse(res, {
            user_id: user.user_id,
            user_name: user.user_name,
            is_active: user.is_active
        }, `User ${is_active ? 'activated' : 'deactivated'} successfully`);
    } catch (error) {
        console.error('Update user status error:', error);
        return BaseController.sendError(res, 'Failed to update user status', [{ error: error.message }]);
    }
};

exports.getDashboardStats = async (req, res) => {
    try {
        // Get total users count
        const totalUsers = await User.count();
        
        // Get active users count
        const activeUsers = await User.count({ where: { is_active: true } });
        
        // Get admin users count
        const adminUsers = await User.count({ where: { role: 'admin' } });
        
        // Get total wallet balance
        const totalBalance = await Wallet.sum('balance') || 0;
        
        // Get total deposits
        const totalDeposits = await Wallet.sum('total_deposited') || 0;
        
        // Get total withdrawals
        const totalWithdrawals = await Wallet.sum('total_withdrawn') || 0;

        const stats = {
            users: {
                total: totalUsers,
                active: activeUsers,
                inactive: totalUsers - activeUsers,
                admins: adminUsers
            },
            wallet: {
                total_balance: parseFloat(totalBalance).toFixed(2),
                total_deposits: parseFloat(totalDeposits).toFixed(2),
                total_withdrawals: parseFloat(totalWithdrawals).toFixed(2)
            }
        };

        return BaseController.sendResponse(res, stats, 'Dashboard statistics fetched successfully');
    } catch (error) {
        console.error('Get dashboard stats error:', error);
        return BaseController.sendError(res, 'Failed to fetch dashboard statistics', [{ error: error.message }]);
    }
};

exports.searchUsers = async (req, res) => {
    try {
        const { query } = req.query;

        if (!query || query.trim().length < 2) {
            return BaseController.sendError(res, 'Search query must be at least 2 characters long');
        }

        const users = await User.findAll({
            where: {
                [Op.or]: [
                    { name: { [Op.like]: `%${query}%` } },
                    { user_name: { [Op.like]: `%${query}%` } },
                    { email: { [Op.like]: `%${query}%` } },
                    { mobile: { [Op.like]: `%${query}%` } }
                ]
            },
            attributes: ['user_id', 'name', 'user_name', 'email', 'mobile', 'role', 'is_active', 'createdAt'],
            include: [{
                model: Wallet,
                attributes: ['balance', 'bonus_balance']
            }],
            limit: 50,
            order: [['createdAt', 'DESC']]
        });

        return BaseController.sendResponse(res, users, `Found ${users.length} users matching "${query}"`);
    } catch (error) {
        console.error('Search users error:', error);
        return BaseController.sendError(res, 'Failed to search users', [{ error: error.message }]);
    }
};
