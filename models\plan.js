'use strict';
const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Plan = sequelize.define('Plan', {
    plan_id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: { 
        notEmpty: true, 
        len: [2, 100] 
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: { 
        notEmpty: true,
        len: [10, 1000]
      }
    },
    amount: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      validate: {
        min: 0.01
      }
    },
    duration: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1
      },
      comment: 'Duration in days'
    },
    return_percentage: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: false,
      validate: {
        min: 0,
        max: 100
      }
    },
    extra_return_percentage: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      defaultValue: 0.00,
      validate: {
        min: 0,
        max: 100
      }
    },
    investment_amount: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      validate: {
        min: 0.01
      }
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false
    }
  }, {
    tableName: 'plan',
    timestamps: true,
    indexes: [
      {
        fields: ['is_active']
      },
      {
        fields: ['name']
      }
    ]
  });

  Plan.associate = (models) => {
    Plan.hasMany(models.Investment, { foreignKey: 'plan_id', as: 'investments' });
  };

  return Plan;
};
