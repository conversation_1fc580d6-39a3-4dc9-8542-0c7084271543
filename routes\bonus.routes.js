const express = require('express');
const router = express.Router();
const { 
    getUserBonusHistory, 
    getUserBonusSummary 
} = require('../controllers/bonus.controller');
const { verifyToken } = require('../middleware/auth.middleware');

// User bonus routes (require authentication)
router.get('/history', verifyToken, getUserBonusHistory);
router.get('/summary', verifyToken, getUserBonusSummary);

module.exports = router;
