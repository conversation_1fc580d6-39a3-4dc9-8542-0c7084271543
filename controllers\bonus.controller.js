const { Bonus, User, Investment, Plan } = require('../models');
const BaseController = require('../helpers/BaseController');
const { Op } = require('sequelize');

// Get user's bonus history
exports.getUserBonusHistory = async (req, res) => {
    try {
        const user_id = req.user.id;
        const { page = 1, limit = 10, bonus_type = 'all' } = req.query;
        const offset = (page - 1) * limit;

        // Build where clause
        const whereClause = { user_id };
        if (bonus_type !== 'all') {
            whereClause.bonus_type = bonus_type;
        }

        const bonuses = await Bonus.findAndCountAll({
            where: whereClause,
            include: [
                {
                    model: User,
                    as: 'sourceUser',
                    attributes: ['user_name', 'name'],
                    required: false
                },
                {
                    model: Investment,
                    as: 'sourceInvestment',
                    attributes: ['investment_id', 'amount'],
                    include: [{
                        model: Plan,
                        as: 'plan',
                        attributes: ['name']
                    }],
                    required: false
                }
            ],
            order: [['createdAt', 'DESC']],
            limit: parseInt(limit),
            offset
        });

        const formattedBonuses = bonuses.rows.map(bonus => ({
            bonus_id: bonus.bonus_id,
            bonus_type: bonus.bonus_type,
            amount: parseFloat(bonus.amount),
            description: bonus.description,
            status: bonus.status,
            source_user: bonus.sourceUser ? {
                user_name: bonus.sourceUser.user_name,
                name: bonus.sourceUser.name
            } : null,
            source_investment: bonus.sourceInvestment ? {
                investment_id: bonus.sourceInvestment.investment_id,
                amount: parseFloat(bonus.sourceInvestment.amount),
                plan_name: bonus.sourceInvestment.plan?.name
            } : null,
            processed_at: bonus.processed_at,
            createdAt: bonus.createdAt
        }));

        return BaseController.sendResponse(res, {
            bonuses: formattedBonuses,
            pagination: {
                current_page: parseInt(page),
                total_pages: Math.ceil(bonuses.count / limit),
                total_records: bonuses.count,
                per_page: parseInt(limit)
            }
        }, 'Bonus history fetched successfully');

    } catch (error) {
        console.error('Get user bonus history error:', error);
        return BaseController.sendError(res, 'Failed to fetch bonus history', [{ error: error.message }]);
    }
};

// Get user's bonus summary
exports.getUserBonusSummary = async (req, res) => {
    try {
        const user_id = req.user.id;

        // Get total bonuses by type
        const bonusSummary = await Bonus.findAll({
            where: { 
                user_id,
                status: 'completed'
            },
            attributes: [
                'bonus_type',
                [Bonus.sequelize.fn('SUM', Bonus.sequelize.col('amount')), 'total_amount'],
                [Bonus.sequelize.fn('COUNT', Bonus.sequelize.col('bonus_id')), 'count']
            ],
            group: ['bonus_type']
        });

        // Get overall totals
        const overallTotal = await Bonus.findOne({
            where: { 
                user_id,
                status: 'completed'
            },
            attributes: [
                [Bonus.sequelize.fn('SUM', Bonus.sequelize.col('amount')), 'total_bonus'],
                [Bonus.sequelize.fn('COUNT', Bonus.sequelize.col('bonus_id')), 'total_transactions']
            ]
        });

        const formattedSummary = bonusSummary.map(item => ({
            bonus_type: item.bonus_type,
            total_amount: parseFloat(item.dataValues.total_amount || 0),
            count: parseInt(item.dataValues.count || 0)
        }));

        return BaseController.sendResponse(res, {
            summary_by_type: formattedSummary,
            overall: {
                total_bonus_earned: parseFloat(overallTotal?.dataValues.total_bonus || 0),
                total_transactions: parseInt(overallTotal?.dataValues.total_transactions || 0)
            }
        }, 'Bonus summary fetched successfully');

    } catch (error) {
        console.error('Get user bonus summary error:', error);
        return BaseController.sendError(res, 'Failed to fetch bonus summary', [{ error: error.message }]);
    }
};

// Admin: Get all bonus transactions
exports.getAllBonusTransactions = async (req, res) => {
    try {
        const { page = 1, limit = 20, bonus_type = 'all', status = 'all' } = req.query;
        const offset = (page - 1) * limit;

        // Build where clause
        const whereClause = {};
        if (bonus_type !== 'all') {
            whereClause.bonus_type = bonus_type;
        }
        if (status !== 'all') {
            whereClause.status = status;
        }

        const bonuses = await Bonus.findAndCountAll({
            where: whereClause,
            include: [
                {
                    model: User,
                    as: 'user',
                    attributes: ['user_name', 'name', 'email']
                },
                {
                    model: User,
                    as: 'sourceUser',
                    attributes: ['user_name', 'name'],
                    required: false
                }
            ],
            order: [['createdAt', 'DESC']],
            limit: parseInt(limit),
            offset
        });

        const formattedBonuses = bonuses.rows.map(bonus => ({
            bonus_id: bonus.bonus_id,
            user: {
                user_name: bonus.user.user_name,
                name: bonus.user.name,
                email: bonus.user.email
            },
            bonus_type: bonus.bonus_type,
            amount: parseFloat(bonus.amount),
            description: bonus.description,
            status: bonus.status,
            source_user: bonus.sourceUser ? {
                user_name: bonus.sourceUser.user_name,
                name: bonus.sourceUser.name
            } : null,
            processed_at: bonus.processed_at,
            admin_notes: bonus.admin_notes,
            createdAt: bonus.createdAt
        }));

        return BaseController.sendResponse(res, {
            bonuses: formattedBonuses,
            pagination: {
                current_page: parseInt(page),
                total_pages: Math.ceil(bonuses.count / limit),
                total_records: bonuses.count,
                per_page: parseInt(limit)
            }
        }, 'All bonus transactions fetched successfully');

    } catch (error) {
        console.error('Get all bonus transactions error:', error);
        return BaseController.sendError(res, 'Failed to fetch bonus transactions', [{ error: error.message }]);
    }
};
